import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import AdminDashboard from './pages/AdminDashboard';
import RegisterCampsite from './pages/RegisterCampsite';
import Home from './pages/Home';
import RegisterUser from './pages/RegisterUser';
import LoginPage from './pages/LoginPage';
import Dashboard from './pages/Dashboard';
import ProtectedRoute from './pages/ProtectedRoute';
import UserDashboard from './pages/UserDashboard';
import Campsite from './pages/Campsite';

function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/login" element={<LoginPage />} />
        <Route path="/register-campsite" element={<RegisterCampsite />} />
        <Route path="/register-user" element={<RegisterUser />} />
        <Route path="/admin/dashboard" element={<ProtectedRoute role="admin"> <AdminDashboard /> </ProtectedRoute>}/>
        <Route path="/operator/dashboard" element={<ProtectedRoute role="operator"> <Dashboard role="operator" /> </ProtectedRoute>}/>
        <Route path="/staff/dashboard" element={<ProtectedRoute role="staff"> <Dashboard role="staff" /> </ProtectedRoute>}/>
        <Route path="/user/dashboard" element={<ProtectedRoute role="user"> <UserDashboard /> </ProtectedRoute>}/>
        <Route path="/campsites/:campsite_id" element={<Campsite />} />
      </Routes>
    </BrowserRouter>
  );
}

export default App;
