import React, { useEffect, useState } from 'react';
import axios from 'axios';

interface User {
    email: string;
    username: string;
    role: string;
}

const UserDashboard = () => {
    const [campsites, setCampsites] = useState<{ id: string; email: string; name: string }[]>([]);

    useEffect(() => {
        // Fetch campsites from the backend
        axios.get('http://localhost:8000/api/campsites')
            .then((response) => {
                console.log('Fetched campsites:', response.data); // Debugging log
                setCampsites(response.data);
            })
            .catch((error) => {
                console.error('Error fetching campsites:', error);
            });
    }, []);

    return (
        <div>
            <h1>User Dashboard</h1>
            <h2>List of Campsites</h2>
            <ul>
                {campsites.map(campsite => (
                    <li key={campsite.id}>{campsite.name} - Operator: {campsite.email}</li>
                ))}
            </ul>
        </div>
    );
};

export default UserDashboard;