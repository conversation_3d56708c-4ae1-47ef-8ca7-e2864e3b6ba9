from sqlalchemy import Column, String, Boolean, create_engine, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from passlib.context import CryptContext

Base = declarative_base()

class User(Base):
    __tablename__ = 'users'

    email = Column(String, primary_key=True, unique=True, nullable=False)
    username = Column(String, unique=True, nullable=False)
    password_hash = Column(String, nullable=False)
    role = Column(String, nullable=False)

class Campsite(Base):
    __tablename__ = 'campsite'

    id = Column(String, primary_key=True, unique=True, nullable=False)
    email = Column(String, ForeignKey('users.email'), unique=True, nullable=False)
    name = Column(String, nullable=False)
    approved = Column(Boolean, nullable=False, default=False)

class CampsiteStaff(Base):
    __tablename__ = 'campsite_staff'

    id = Column(String, primary_key=True, unique=True, nullable=False)
    campsite_id = Column(String, ForeignKey('campsite.id'), nullable=False)
    email = Column(String, ForeignKey('users.email'), unique=True, nullable=False)
    

DATABASE_URL = "**************************************/campgrid"
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base.metadata.create_all(bind=engine)

pwd_context = CryptContext(schemes=["bcrypt"], bcrypt__default_rounds=12)

def create_user(email: str, username: str, password: str, role: str):
    """Create a new user and add it to the database."""
    password_hash = pwd_context.hash(password)
    session = SessionLocal()
    try:
        new_user = User(email=email, username=username, password_hash=password_hash, role=role)
        session.add(new_user)
        session.commit()
    except Exception as e:
        session.rollback()
        raise e
    finally:
        session.close()

def initialize_admin_user():
    """Initialize the database with a default admin user."""
    session = SessionLocal()
    try:
        # Check if the admin user already exists
        admin_user = session.query(User).filter_by(email='<EMAIL>').first()
        if not admin_user:
            create_user(
                email='<EMAIL>',
                username='admin',
                password='admin',
                role='admin'
            )
    except Exception as e:
        session.rollback()
        raise e
    finally:
        session.close()

# Call the function to initialize the admin user
initialize_admin_user()